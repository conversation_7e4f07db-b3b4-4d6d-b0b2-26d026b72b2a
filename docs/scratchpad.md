# Lessons

- For website image paths, always use the correct relative path (e.g., 'images/filename.png') and ensure the images directory exists
- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- When using Jest, a test suite can fail even if all individual tests pass, typically due to issues in suite-level setup code or lifecycle hooks
- When using Next.js with `next/font` and a custom Babel config, you need to explicitly enable SWC in next.config.js/ts with `experimental: { forceSwcTransforms: true }`
- To fix hydration errors in Next.js when browser extensions modify HTML attributes, use the `suppressHydrationWarning` attribute on the affected element (usually the `html` tag)
- When using React Hook Form with controlled inputs, always provide a defined default value (e.g., use 0 instead of undefined for number inputs) to avoid React warnings about switching between controlled and uncontrolled inputs
- When implementing AI-based features, always add robust error handling and fallback mechanisms to ensure the application works even when the AI service fails
- When deploying Next.js apps with Firebase Admin SDK to Netlify:
  - Environment variables in `next.config.js` must be strings, not booleans (use `'true'` instead of `true`)
  - Skip Firebase Admin SDK initialization during build time using a conditional check
  - Use dynamic imports for Firebase Admin SDK to prevent it from being included in client bundles
  - Create mock implementations of Firebase services for build time to prevent JSON parsing errors with service account keys
  - Export consistent Firebase Admin instances (app, auth, db) to simplify imports across API routes
- When working with Supabase storage, use the dashboard to create buckets and set policies rather than trying to do it via SQL or API calls, as the storage system is separate from the database
- When using Supabase with foreign key constraints, ensure that records exist in the referenced tables before inserting new records. For example, when using Supabase auth with a users table that has foreign key relationships, make sure to create corresponding records in the users table for authenticated users
- When working with date fields from Supabase in Laravel Blade templates, always check if the date is a string or a Carbon object before calling format() to avoid "Call to a member function format() on string" errors
- When using Supabase for user management, the service role key is required for admin operations like creating users. The anon key has limited permissions and can't create users directly without email verification.
- Need to use Supabase service role key for admin operations like creating users
- Add the service role key to the .env file: `SUPABASE_SERVICE_ROLE=your_service_role_key_here`
- Configure the service role key in config/supabase.php
- When using only Supabase for authentication (no Laravel Auth):
  - Store Supabase token in session instead of creating Laravel users
  - Use a custom middleware (SupabaseAuth) to verify Supabase tokens
  - Update routes to use the Supabase middleware instead of Laravel's auth middleware
  - Handle logout by signing out from Supabase and clearing the session
- For date handling in JavaScript, always validate dates with isNaN(date.getTime()) to check if a date is valid before using it in calculations or comparisons
- When removing Vite from a Laravel project, load Tailwind CSS directly from a CDN and create custom CSS files in the public directory

## Windsurf learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- Use 'gpt-4o' as the model name for OpenAI's GPT-4 with vision capabilities
- In Next.js 15, route parameters (`params`) in client components should be accessed using the `useParams()` hook from 'next/navigation' instead of directly from props, as params are now a Promise object that needs to be unwrapped

# Scratchpad

## BarcodeCafe-QR-Menu Project Overview

### Project Description
A digital menu and customer portal for Barcode Cafe built with Next.js 15, React 19, and Firebase. The application features a QR code-based menu system with customer authentication, ordering capabilities, and admin management.

### Key Features
1. **Interactive Digital Menu**
   - Menu categories and items stored in Firebase Firestore
   - Item details with images, descriptions, and stock status
   - Cart functionality for ordering

2. **Authentication System**
   - Email/password and Google authentication
   - Email verification flow
   - Password reset functionality
   - Admin role management

3. **Customer Portal**
   - User profiles and dashboard
   - Order history tracking
   - Address management
   - Gift card management
   - Reviews system

4. **Admin Features**
   - Menu item management
   - Category management
   - Order processing
   - User management

5. **UI/UX Features**
   - Dark/light mode support
   - Internationalization (English/Arabic)
   - RTL/LTR layout support
   - Responsive design

### Technical Architecture
- **Frontend**: Next.js with App Router, React 19, Tailwind CSS
- **Backend**: Firebase (Authentication, Firestore, Storage)
- **State Management**: React Context API (Auth, Cart, Locale)
- **UI Components**: Mix of custom components and shadcn/ui
- **Icons**: Font Awesome

### Project Structure
- `/src/app`: Next.js App Router pages
- `/src/components`: Reusable UI components
- `/src/contexts`: React Context providers
- `/src/lib`: Utility functions and Firebase configuration
- `/src/types`: TypeScript type definitions
- `/src/locales`: Translation files
- `/public`: Static assets

### Development Workflow
- Create a new branch before starting any task
- Write unit tests after completing tasks
- Commit changes and create pull requests

## Current Task: Menu Redesign

**Status**: Completed (Last updated: June 4, 2025)

**Task Description**:  
Redesign the public menu page to match the new layout and style while maintaining the existing color palette and preserving all current functionality.  

**Progress**:  
- [x] Create new branch `feature/menu-redesign`
- [x] Review current menu page structure and components
- [x] Update header section with new layout
- [x] Add hero image to header
- [x] Redesign categories navigation
- [x] Update menu items to grid layout
- [x] Enhance item detail sheet
- [x] Update cart button and cart sheet
- [x] Replace logo with SVG version
- [x] Add search functionality to header
- [x] Move social icons to footer
- [x] Test responsiveness and dark mode
- [x] Add missing translations for UI elements
- [x] Fix accessibility issues
- [x] Fix currency display to use SAR instead of dollar signs
- [x] Commit changes and push to remote
- [ ] Write unit tests
- [ ] Create PR

**Implementation Plan**:  
1. Update header section with logo and social icons
2. Add hero image from Unsplash to create more visual impact
3. Redesign categories navigation to use pills instead of icons
4. Change menu items from list to grid layout
5. Add featured badge on featured items
6. Enhance item detail sheet with better layout
7. Update data models to support new design features
   - Added `description` field to Category model
   - Added `ingredients` and `allergens` fields to MenuItem model
8. Update cart button and cart sheet with new design
9. Replace logo.jpg with logo-white.svg for better quality
10. Add search functionality to the header for better user experience
11. Move social icons to a proper footer section
12. Ensure all changes maintain existing color palette and dark mode compatibility
13. Test responsiveness across different screen sizes

**Key Design Changes**:
- Header: Added hero image with coffee beans background and overlay gradient
- Logo: Replaced logo.jpg with logo-white.svg, removed rounded container and text elements
- Search: Added search functionality in the header with real-time filtering of menu items
- Social Icons: Moved from header to footer with hover effects and better spacing
- Footer: Created a proper footer section with social icons and copyright notice
- Categories: Changed from icon circles to horizontal pills with active state highlighting
- Menu Items: Switched from vertical list to grid layout with cards
- Item Cards: Added featured badge, improved layout with image on top
- Item Detail: Enhanced sheet with better organization of details and allergen tags
- Cart Button: Updated with hover effects and improved counter badge

**Testing Notes**:
- Verified dark mode compatibility across all components
- Tested responsive layout on mobile, tablet, and desktop viewports
- Ensured RTL support for Arabic locale
- Confirmed all functionality works as expected (adding to cart, changing quantity, search functionality, etc.)
- Verified search results display correctly for both title and description matches
- Confirmed social icons in footer are properly displayed and functional
- Fixed TypeScript errors by ensuring proper property names from MenuItem interface
- Added missing translations for search results and other UI elements
- Fixed accessibility issue with SheetContent component by adding required SheetTitle
- Fixed currency display to consistently use SAR instead of dollar signs

**Additional Improvements**:
- Added missing translations for:
  - common.cafeDescription
  - menu.add
  - menu.searchResults
  - common.currency
  - common.min
  - menu.categoryDescription
  - menu.noSearchResults
  - common.allRightsReserved
- Fixed accessibility issue with DialogContent requiring a DialogTitle
- Made SheetTitle visually hidden with sr-only class but accessible to screen readers
- Updated price displays to use the correct currency (SAR) throughout the application

## Current Task: Order Details Implementation

**Status**: Completed (Last updated: June 4, 2025)

**Task Description**:  
Implement the Order Details feature inside the Customer's Order History section to allow customers to view detailed information about their orders.

**Progress**:  
- [x] Create new branch `feat/order-details-implementation`
- [x] Review existing order history page and data models
- [x] Create dynamic route for order details (`/customer/orders/[id]`)
- [x] Implement order details page with proper layout
- [x] Add authentication and authorization checks
- [x] Add order summary section with subtotal, tax, and total
- [x] Add order actions (print receipt, cancel order)
- [x] Update localization files with new translation keys
- [x] Fix currency display in Order History page
- [x] Improve payment method display with proper localization
- [x] Commit changes and push to remote
- [x] Write unit tests
- [x] Create PR

**Implementation Details**:  
1. Created a new dynamic route page `/src/app/customer/orders/[id]/page.tsx`
2. Implemented authentication checks to ensure only logged-in users can access the page
3. Added authorization check to verify the order belongs to the current user
4. Displayed comprehensive order information:
   - Order status with color-coded badge
   - Order date and time
   - Payment method
   - Order items with options and prices
   - Special instructions (if any)
   - Order summary (subtotal, tax, total)
5. Added action buttons:
   - Print receipt button
   - Cancel order button (only shown for orders with status ORDER_PLACED)
6. Updated the `formatDate` utility function to support showing time
7. Added comprehensive localization support in both English and Arabic
8. Fixed currency display in Order History page to use SAR instead of dollar signs
9. Improved payment method display with proper translation keys

**Key Features**:
- Dynamic order fetching based on URL parameter
- User-specific authorization to prevent unauthorized access
- Consistent styling with the rest of the application
- Responsive layout for all screen sizes
- Full localization support for all UI elements
- Proper error handling and loading states

**Testing Notes**:
- Verified authentication redirects work correctly
- Confirmed authorization check prevents viewing others' orders
- Tested responsive layout on mobile, tablet, and desktop viewports
- Verified all order information displays correctly
- Confirmed print functionality works as expected
- Verified proper localization in both English and Arabic
- Ensured consistent currency display throughout the application

## Current Task: Delivery Options During Checkout

**Status**: Completed (Last updated: June 5, 2025)

**Task Description**:  
Implement functionality for handling delivery options during checkout with the following requirements:
1. Prompt customers to select delivery type:
   - Table Number
   - Pick Up
   - Delivery
2. For Delivery option:
   - Retrieve delivery zone data from Firestore's `deliveryZones` collection
   - Apply appropriate delivery fee based on selected zone
   - Add delivery fee to cart total

**Progress**:  
- [x] Create new branch `feature/delivery-options-checkout`
- [x] Explore the current checkout flow and components
- [x] Identify where to add delivery type selection UI
- [x] Create interface for delivery options
- [x] Implement delivery type selection component
- [x] Add functionality to fetch delivery zones from Firestore
- [x] Implement logic to calculate and apply delivery fees
- [x] Update cart total to include delivery fees
- [x] Add necessary translations
- [X] Test the implementation
- [X] Write unit tests
- [X] Commit changes and push to remote
- [X] Create PR

## Current Task: Add Caffeine and Allergic Information to Menu Items

**Status**: In Progress (Last updated: June 11, 2025)

**Task Description**:  
Add Caffeine and Allergic information alongside the existing Kcal information in menu items. Ensure proper translation support is maintained for all new fields.

**Progress**:  
- [x] Create new branch `feature/add-caffeine-allergic-info`
- [x] Check the MenuItem interface to confirm caffeine and allergens fields exist
- [x] Add caffeine field to MenuItem interface
- [x] Add translation keys for caffeine and allergens
- [x] Update menu item cards to display caffeine content
- [x] Update item detail sheet to display caffeine content
- [x] Update allergens section styling to match the rest of the UI
- [x] Test the implementation with hardcoded sample data
- [x] Commit changes
- [x] Push changes to remote
- [x] Task completed

**Implementation Plan**:
1. Update the menu item cards to display caffeine content alongside Kcal
2. Update the item detail sheet to display caffeine and allergens information
3. Ensure proper translation keys are used for all new UI elements
4. Test the implementation across different scenarios and languages
5. Write unit tests for the updated components
6. Commit changes and create a PR

**Implementation Plan**:
1. Create a new branch for this feature
2. Explore the current checkout flow to understand where to integrate delivery options
3. Design and implement a delivery type selection component
4. Create functionality to fetch delivery zones from Firestore
5. Implement logic to calculate delivery fees based on selected zone
6. Update the cart total calculation to include delivery fees
7. Add necessary translations for new UI elements
8. Test the implementation across different scenarios
9. Write unit tests for the new components and functionality
10. Commit changes and create a PR

## Lessons

- When using dynamic imports with Next.js, it's important to define prop types for the dynamically imported components to avoid TypeScript errors
- Event handlers in React components should have explicit type annotations to avoid implicit any errors
- When using Radix UI components like AlertDialog, make sure to properly implement the action handlers
- For testing components with context dependencies, mock the contexts to provide the necessary values
- When testing components that use Firestore functions, mock the functions to avoid actual database calls
- When mocking components in tests, add data-testid attributes to key elements to make them easier to query
- In Jest tests, variable declarations must come before they are used in mock implementations
- When testing components that use localization, mock the localization context to return keys instead of translated text
- For components that render conditionally (like loading states), use data-testid attributes instead of text content for more reliable tests
- When testing tab-based interfaces, check for aria-selected attributes rather than relying on visual changes
- Instead of testing mocked component internals, focus on testing the integration with external services (like Firestore)
- When testing CRUD operations, directly test the function calls rather than simulating complex UI interactions



- Radius (km)
- Pickup: Car Number - Color - Model.
- Promotion
- Rose 30%, Tiffany 70%