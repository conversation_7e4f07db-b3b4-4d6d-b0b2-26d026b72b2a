'use client';

import { 
  createContext, 
  useContext, 
  useState, 
  useEffect, 
  ReactNode 
} from 'react';
import { MenuItem, OrderStatus, PaymentMethod, DeliveryZoneType } from '@/types/models';
import { DeliveryOption, DeliveryType } from '@/types/delivery';
import { useAuth } from './AuthContext';
import { createOrder } from '@/lib/firebase/firestore';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';

// Define the cart item type which extends MenuItem with quantity
export interface CartItem extends Omit<MenuItem, 'isFeatured' | 'isActive' | 'isAvailableForDelivery'> {
  quantity: number;
}

// Cart context type
interface CartContextType {
  cartItems: CartItem[];
  addToCart: (item: MenuItem, quantity: number) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  removeFromCart: (itemId: string) => void;
  clearCart: () => void;
  placeOrder: () => Promise<void>;
  isCartEmpty: boolean;
  cartTotal: number;
  cartItemsCount: number;
  deliveryOption: DeliveryOption | null;
  setDeliveryOption: (option: DeliveryOption) => void;
  totalWithDelivery: number;
}

// Create the cart context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Custom hook to use the cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

const CART_STORAGE_KEY = 'barcode-cafe-cart';

interface CartProviderProps {
  children: ReactNode;
}

// Cart Provider Component
export function CartProvider({ children }: CartProviderProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const { user } = useAuth();
  const router = useRouter();

  // Load cart items and delivery option from localStorage on initial mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem(CART_STORAGE_KEY);
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
        } catch (error) {
          console.error('Failed to parse cart from localStorage:', error);
        }
      }

      const savedDeliveryOption = localStorage.getItem(`${CART_STORAGE_KEY}-delivery`);
      if (savedDeliveryOption) {
        try {
          const parsedOption = JSON.parse(savedDeliveryOption);
          setDeliveryOption(parsedOption);
        } catch (error) {
          console.error('Failed to parse delivery option from localStorage:', error);
        }
      }
      
      setIsInitialized(true);
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cartItems));
    }
  }, [cartItems, isInitialized]);

  // Save delivery option to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined' && deliveryOption) {
      localStorage.setItem(`${CART_STORAGE_KEY}-delivery`, JSON.stringify(deliveryOption));
    }
  }, [deliveryOption, isInitialized]);

  // Add an item to the cart
  const addToCart = (item: MenuItem, quantity: number) => {
    setCartItems(prevItems => {
      // Check if the item is already in the cart
      const existingItemIndex = prevItems.findIndex(cartItem => cartItem.id === item.id);
      
      if (existingItemIndex >= 0) {
        // Item exists, update its quantity
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // Item doesn't exist, add it with the specified quantity
        return [...prevItems, {
          ...item,
          quantity
        }];
      }
    });
  };

  // Update quantity of an item in the cart
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    
    setCartItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // Remove an item from the cart
  const removeFromCart = (itemId: string) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  // Clear the cart
  const clearCart = () => {
    setCartItems([]);
    setDeliveryOption(null);
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`${CART_STORAGE_KEY}-delivery`);
    }
  };

  // Place an order
  const placeOrder = async () => {
    if (!user) {
      // User is not logged in, redirect to sign in page
      router.push('/signin');
      return;
    }
    
    try {
      // Create order items from cart items
      const orderItems = cartItems.map(item => ({
        id: item.id,
        name: item.title,
        price: item.price,
        quantity: item.quantity,
      }));
      
      // Calculate subtotal (without delivery fee)
      const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      // Calculate total with delivery fee if applicable
      const total = deliveryOption?.fee ? subtotal + deliveryOption.fee : subtotal;
      
      // Create the order with delivery information
      await createOrder({
        userId: user.uid,
        items: orderItems,
        status: OrderStatus.ORDER_PLACED,
        subtotal,
        deliveryFee: deliveryOption?.fee || 0,
        total,
        paymentMethod: PaymentMethod.CREDIT_CARD,
        deliveryType: deliveryOption?.type === DeliveryType.DELIVERY ? DeliveryZoneType.DELIVERY : 
                   deliveryOption?.type === DeliveryType.TABLE ? DeliveryZoneType.IN_HOUSE_TABLES : 
                   DeliveryZoneType.PICK_UP,
        tableNumber: deliveryOption?.type === DeliveryType.TABLE ? deliveryOption.tableNumber : undefined,
        deliveryAddress: deliveryOption?.type === DeliveryType.DELIVERY ? deliveryOption.deliveryAddress : undefined,
        deliveryZoneId: deliveryOption?.type === DeliveryType.DELIVERY ? deliveryOption.deliveryZone?.id : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      
      // Clear the cart after successful order
      clearCart();
      
      // Show success message
      toast({
        title: 'Order placed successfully',
        description: 'Your order has been placed successfully',
        variant: 'success',
      });
      
      // Redirect to order history
      router.push('/customer/orders');
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Failed to place order',
        description: 'There was an error placing your order. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Computed properties
  const isCartEmpty = cartItems.length === 0;
  const cartTotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const cartItemsCount = cartItems.reduce((count, item) => count + item.quantity, 0);
  const totalWithDelivery = deliveryOption?.fee ? cartTotal + deliveryOption.fee : cartTotal;

  // Context value
  const value = {
    cartItems,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    placeOrder,
    isCartEmpty,
    cartTotal,
    cartItemsCount,
    deliveryOption,
    setDeliveryOption,
    totalWithDelivery,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
} 